<script lang="ts" setup>
const props = defineProps({
  value: {
    type: [String, Number],
    default: '',
  },
  options: {
    type: Array<Record<string, any>>,
    default: () => [],
  },
  itemTitle: {
    type: String,
    default: 'label',
  },
  itemValue: {
    type: String,
    default: 'value',
  },
  fillRow: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:value'])

const currentValue = ref(props.value)

watch(() => currentValue.value, val => {
  emit('update:value', val)
})
</script>

<template>
  <VBtnToggle
    v-model="currentValue"
    class="pa-0 h-auto border-0"
    :class="[fillRow ? 'w-100' : '']"
    density="compact"
    mandatory
    color="primary"
  >
    <template
      v-for="(item, index) in options"
      :key="index"
    >
      <VBtn
        :class="[fillRow ? 'flex-1-0' : '']"
        :max-height="30"
        type="primary"
        :value="item[itemValue]"
      >
        {{ item[itemTitle] }}
      </VBtn>
    </template>
  </VBtnToggle>
</template>

<style lang="scss">
.network-type-radio-group {
  &_divider {
    block-size: 30px;
    inline-size: 1px;
  }

  .v-btn__content {
    font-size: 13px;
  }
}
</style>
